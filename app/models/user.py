import sqlite3
from werkzeug.security import generate_password_hash, check_password_hash
from flask_login import UserMixin
from app.models.db import get_db

class User(UserMixin):
    def __init__(self, id, username, email, password, score=1000, matches_played=0, matches_won=0, has_paid=False):
        self.id = id
        self.username = username
        self.email = email
        self.password = password
        self.score = score
        self.matches_played = matches_played
        self.matches_won = matches_won
        self.has_paid = has_paid

    @staticmethod
    def create(username, email, password):
        db = get_db()
        try:
            cursor = db.execute(
                'INSERT INTO users (username, email, password) VALUES (?, ?, ?)',
                (username, email, generate_password_hash(password))
            )
            db.commit()
            return User.get_by_id(cursor.lastrowid)
        except sqlite3.IntegrityError:
            return None

    @staticmethod
    def get_by_id(user_id):
        db = get_db()
        user = db.execute('SELECT * FROM users WHERE id = ?', (user_id,)).fetchone()
        if user is None:
            return None
        return User(
            id=user['id'],
            username=user['username'],
            email=user['email'],
            password=user['password'],
            score=user['score'],
            matches_played=user['matches_played'],
            matches_won=user['matches_won'],
            has_paid=bool(user['has_paid']) if 'has_paid' in user.keys() else False
        )

    @staticmethod
    def get_by_username(username):
        db = get_db()
        user = db.execute('SELECT * FROM users WHERE username = ?', (username,)).fetchone()
        if user is None:
            return None
        return User(
            id=user['id'],
            username=user['username'],
            email=user['email'],
            password=user['password'],
            score=user['score'],
            matches_played=user['matches_played'],
            matches_won=user['matches_won'],
            has_paid=bool(user['has_paid']) if 'has_paid' in user.keys() else False
        )

    @staticmethod
    def get_by_email(email):
        db = get_db()
        user = db.execute('SELECT * FROM users WHERE email = ?', (email,)).fetchone()
        if user is None:
            return None
        return User(
            id=user['id'],
            username=user['username'],
            email=user['email'],
            password=user['password'],
            score=user['score'],
            matches_played=user['matches_played'],
            matches_won=user['matches_won'],
            has_paid=bool(user['has_paid']) if 'has_paid' in user.keys() else False
        )

    def check_password(self, password):
        return check_password_hash(self.password, password)

    def update_score(self, new_score):
        db = get_db()
        db.execute('UPDATE users SET score = ? WHERE id = ?', (new_score, self.id))
        db.commit()
        self.score = new_score

    def increment_matches_played(self):
        db = get_db()
        db.execute('UPDATE users SET matches_played = matches_played + 1 WHERE id = ?', (self.id,))
        db.commit()
        self.matches_played += 1

    def increment_matches_won(self):
        db = get_db()
        db.execute('UPDATE users SET matches_won = matches_won + 1 WHERE id = ?', (self.id,))
        db.commit()
        self.matches_won += 1

    def update_payment_status(self, has_paid):
        db = get_db()
        db.execute('UPDATE users SET has_paid = ? WHERE id = ?', (has_paid, self.id))
        db.commit()
        self.has_paid = has_paid

    @staticmethod
    def get_users_by_score_range(score, range_value=100):
        db = get_db()
        users = db.execute(
            'SELECT * FROM users WHERE score BETWEEN ? AND ? ORDER BY score DESC',
            (score - range_value, score + range_value)
        ).fetchall()

        return [User(
            id=user['id'],
            username=user['username'],
            email=user['email'],
            password=user['password'],
            score=user['score'],
            matches_played=user['matches_played'],
            matches_won=user['matches_won'],
            has_paid=bool(user['has_paid']) if 'has_paid' in user.keys() else False
        ) for user in users]

    @staticmethod
    def get_top_users(limit=10):
        """Get the top users by score"""
        db = get_db()
        users = db.execute(
            'SELECT * FROM users ORDER BY score DESC LIMIT ?',
            (limit,)
        ).fetchall()

        return [User(
            id=user['id'],
            username=user['username'],
            email=user['email'],
            password=user['password'],
            score=user['score'],
            matches_played=user['matches_played'],
            matches_won=user['matches_won'],
            has_paid=bool(user['has_paid']) if 'has_paid' in user.keys() else False
        ) for user in users]

    @staticmethod
    def search(query, limit=20):
        """Search for users by username or email"""
        db = get_db()
        search_query = f"%{query}%"
        users = db.execute(
            'SELECT * FROM users WHERE username LIKE ? OR email LIKE ? ORDER BY score DESC LIMIT ?',
            (search_query, search_query, limit)
        ).fetchall()

        return [User(
            id=user['id'],
            username=user['username'],
            email=user['email'],
            password=user['password'],
            score=user['score'],
            matches_played=user['matches_played'],
            matches_won=user['matches_won'],
            has_paid=bool(user['has_paid']) if 'has_paid' in user.keys() else False
        ) for user in users]
