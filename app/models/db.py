import sqlite3
import os
from flask import g, current_app

DATABASE = './battle_app.db'

def get_db():
    if 'db' not in g:
        # Ensure the directory exists
        os.makedirs(os.path.dirname(DATABASE), exist_ok=True)
        g.db = sqlite3.connect(
            DATABASE,
            detect_types=sqlite3.PARSE_DECLTYPES
        )
        g.db.row_factory = sqlite3.Row
    return g.db

def close_db(e=None):
    db = g.pop('db', None)
    if db is not None:
        db.close()

def fix_matches_played():
    """Fix the matches_played count for all users to match the actual number of completed matches"""
    db = get_db()

    # Get all users
    users = db.execute('SELECT id FROM users').fetchall()

    for user in users:
        user_id = user['id']

        # Count total completed matches for this user
        total_completed_matches = db.execute(
            '''SELECT COUNT(*) FROM matches
               WHERE (user1_id = ? OR user2_id = ?) AND status = 'completed' ''',
            (user_id, user_id)
        ).fetchone()[0]

        # Update the user's matches_played count
        db.execute(
            'UPDATE users SET matches_played = ? WHERE id = ?',
            (total_completed_matches, user_id)
        )

    # Commit all changes
    db.commit()
    print("Fixed matches_played count for all users")

def fix_match_statistics():
    """Fix the match statistics (matches_played, matches_won) for all users based on actual match data"""
    db = get_db()

    # Get all users
    users = db.execute('SELECT id FROM users').fetchall()

    for user in users:
        user_id = user['id']

        # Count total completed matches for this user
        total_completed_matches = db.execute(
            '''SELECT COUNT(*) FROM matches
               WHERE (user1_id = ? OR user2_id = ?) AND status = 'completed' ''',
            (user_id, user_id)
        ).fetchone()[0]

        # Count matches won (where user is the winner)
        matches_won = db.execute(
            '''SELECT COUNT(*) FROM matches
               WHERE winner_id = ? AND status = 'completed' ''',
            (user_id,)
        ).fetchone()[0]

        # Count drawn matches (where winner_id is NULL)
        matches_drawn = db.execute(
            '''SELECT COUNT(*) FROM matches
               WHERE (user1_id = ? OR user2_id = ?) AND status = 'completed' AND winner_id IS NULL''',
            (user_id, user_id)
        ).fetchone()[0]

        # Calculate matches lost (total - won - drawn)
        matches_lost = total_completed_matches - matches_won - matches_drawn

        # Update the user's matches_played and matches_won in the database
        db.execute(
            'UPDATE users SET matches_played = ?, matches_won = ? WHERE id = ?',
            (total_completed_matches, matches_won, user_id)
        )

        print(f"User {user_id}: Played {total_completed_matches}, Won {matches_won}, Drawn {matches_drawn}, Lost {matches_lost}")

    # Commit all changes
    db.commit()
    print("Fixed match statistics for all users")

def fix_rating_history():
    """Ensure all completed matches have corresponding rating history entries"""
    import datetime
    from app.models.rating_history import RatingHistory

    db = get_db()

    # Get all completed matches
    matches = db.execute(
        '''SELECT id, user1_id, user2_id, user1_score, user2_score, winner_id
           FROM matches WHERE status = 'completed' '''
    ).fetchall()

    for match in matches:
        match_id = match['id']
        user1_id = match['user1_id']
        user2_id = match['user2_id']

        # Check if rating history exists for this match
        user1_history = db.execute(
            'SELECT COUNT(*) FROM rating_history WHERE match_id = ? AND user_id = ?',
            (match_id, user1_id)
        ).fetchone()[0]

        user2_history = db.execute(
            'SELECT COUNT(*) FROM rating_history WHERE match_id = ? AND user_id = ?',
            (match_id, user2_id)
        ).fetchone()[0]

        # If either user is missing rating history, create it
        if user1_history == 0 or user2_history == 0:
            # Get current user scores
            user1 = db.execute('SELECT score FROM users WHERE id = ?', (user1_id,)).fetchone()
            user2 = db.execute('SELECT score FROM users WHERE id = ?', (user2_id,)).fetchone()

            # Create timestamp
            timestamp = datetime.datetime.now().isoformat()

            # For simplicity, we'll use the current score as both old and new rating
            # This is not ideal but ensures the graph shows all matches
            if user1_history == 0:
                db.execute(
                    '''INSERT INTO rating_history
                       (user_id, old_rating, new_rating, match_id, timestamp)
                       VALUES (?, ?, ?, ?, ?)''',
                    (user1_id, user1['score'], user1['score'], match_id, timestamp)
                )
                print(f"Created missing rating history for user {user1_id}, match {match_id}")

            if user2_history == 0:
                db.execute(
                    '''INSERT INTO rating_history
                       (user_id, old_rating, new_rating, match_id, timestamp)
                       VALUES (?, ?, ?, ?, ?)''',
                    (user2_id, user2['score'], user2['score'], match_id, timestamp)
                )
                print(f"Created missing rating history for user {user2_id}, match {match_id}")

    # Commit all changes
    db.commit()
    print("Fixed rating history for all matches")

def init_db():
    db = get_db()

    # Create users table
    db.execute('''
    CREATE TABLE IF NOT EXISTS users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        username TEXT UNIQUE NOT NULL,
        password TEXT NOT NULL,
        email TEXT UNIQUE NOT NULL,
        score INTEGER DEFAULT 1000,
        matches_played INTEGER DEFAULT 0,
        matches_won INTEGER DEFAULT 0,
        has_paid BOOLEAN DEFAULT 0
    )
    ''')

    # Add has_paid column to existing users table if it doesn't exist
    try:
        db.execute('ALTER TABLE users ADD COLUMN has_paid BOOLEAN DEFAULT 0')
        db.commit()
    except sqlite3.OperationalError:
        # Column already exists
        pass

    # Create questions table
    db.execute('''
    CREATE TABLE IF NOT EXISTS questions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        question TEXT NOT NULL,
        option_a TEXT NOT NULL,
        option_b TEXT NOT NULL,
        option_c TEXT NOT NULL,
        option_d TEXT NOT NULL,
        correct_answer TEXT NOT NULL
    )
    ''')

    # Create matches table
    db.execute('''
    CREATE TABLE IF NOT EXISTS matches (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user1_id INTEGER NOT NULL,
        user2_id INTEGER NOT NULL,
        start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        end_time TIMESTAMP,
        status TEXT DEFAULT 'pending',
        user1_score INTEGER DEFAULT 0,
        user2_score INTEGER DEFAULT 0,
        winner_id INTEGER,
        FOREIGN KEY (user1_id) REFERENCES users (id),
        FOREIGN KEY (user2_id) REFERENCES users (id),
        FOREIGN KEY (winner_id) REFERENCES users (id)
    )
    ''')

    # Create user_answers table
    db.execute('''
    CREATE TABLE IF NOT EXISTS user_answers (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        match_id INTEGER NOT NULL,
        user_id INTEGER NOT NULL,
        question_id INTEGER NOT NULL,
        answer TEXT,
        is_correct BOOLEAN,
        FOREIGN KEY (match_id) REFERENCES matches (id),
        FOREIGN KEY (user_id) REFERENCES users (id),
        FOREIGN KEY (question_id) REFERENCES questions (id)
    )
    ''')

    # Create match_questions table to store which questions are in each match
    db.execute('''
    CREATE TABLE IF NOT EXISTS match_questions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        match_id INTEGER NOT NULL,
        question_id INTEGER NOT NULL,
        question_order INTEGER NOT NULL,
        FOREIGN KEY (match_id) REFERENCES matches (id),
        FOREIGN KEY (question_id) REFERENCES questions (id)
    )
    ''')

    # Create practice_results table to track practice performance
    db.execute('''
    CREATE TABLE IF NOT EXISTS practice_results (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        subject TEXT,
        topic TEXT,
        correct_answers INTEGER NOT NULL,
        total_questions INTEGER NOT NULL,
        date_taken TEXT NOT NULL,
        FOREIGN KEY (user_id) REFERENCES users (id)
    )
    ''')

    # Create friend_requests table to track friend requests
    db.execute('''
    CREATE TABLE IF NOT EXISTS friend_requests (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        sender_id INTEGER NOT NULL,
        receiver_id INTEGER NOT NULL,
        status TEXT NOT NULL,
        created_at TEXT NOT NULL,
        updated_at TEXT,
        FOREIGN KEY (sender_id) REFERENCES users (id),
        FOREIGN KEY (receiver_id) REFERENCES users (id)
    )
    ''')

    # Create friends table to track friendships
    db.execute('''
    CREATE TABLE IF NOT EXISTS friends (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        friend_id INTEGER NOT NULL,
        created_at TEXT NOT NULL,
        FOREIGN KEY (user_id) REFERENCES users (id),
        FOREIGN KEY (friend_id) REFERENCES users (id)
    )
    ''')

    # Create rating_history table to track user rating changes
    db.execute('''
    CREATE TABLE IF NOT EXISTS rating_history (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        old_rating INTEGER NOT NULL,
        new_rating INTEGER NOT NULL,
        match_id INTEGER NOT NULL,
        timestamp TEXT NOT NULL,
        FOREIGN KEY (user_id) REFERENCES users (id),
        FOREIGN KEY (match_id) REFERENCES matches (id)
    )
    ''')

    db.commit()
