{% extends 'base.html' %}

{% block title %}All Matches - JEE Battle{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ url_for('main.dashboard') }}">Dashboard</a></li>
                <li class="breadcrumb-item active" aria-current="page">All Matches</li>
            </ol>
        </nav>
    </div>
</div>

<div class="row">
    <div class="col-md-4">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Profile</h5>
            </div>
            <div class="card-body">
                <h3>{{ user.username }}</h3>
                <p class="text-muted">{{ user.email }}</p>
                <hr>
                <div class="d-flex justify-content-between mb-2">
                    <strong>Score:</strong>
                    <span>{{ user.score }}</span>
                </div>
                <div class="d-flex justify-content-between mb-2">
                    <strong>Matches Played:</strong>
                    <span>{{ user.matches_played }}</span>
                </div>
                <div class="d-flex justify-content-between mb-2">
                    <strong>Matches Won:</strong>
                    <span>{{ user.matches_won }}</span>
                </div>
                <div class="d-flex justify-content-between mb-2">
                    <strong>Win Rate:</strong>
                    <span>
                        {% if user.matches_played > 0 %}
                            {{ (user.matches_won / user.matches_played * 100) | round(1) }}%
                        {% else %}
                            0%
                        {% endif %}
                    </span>
                </div>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Start a Battle</h5>
            </div>
            <div class="card-body text-center">
                {% if active_match %}
                    <p>You have an active battle!</p>
                    <a href="{{ url_for('battle.battle_room', match_id=active_match.id) }}" class="btn btn-primary btn-lg">Continue Battle</a>
                {% else %}
                    <p>Ready to test your JEE knowledge?</p>
                    <a href="{{ url_for('battle.start_battle') }}" class="btn btn-primary btn-lg">Start Battle</a>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="col-md-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">All Battles</h5>
            </div>
            <div class="card-body">
                {% if match_data %}
                    <div class="table-responsive">
                        <table class="table table-hover table-stack-mobile">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Opponent</th>
                                    <th>Your Score</th>
                                    <th>Opponent Score</th>
                                    <th>Result</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for data in match_data %}
                                    {% set match = data.match %}
                                    {% set opponent = data.opponent %}
                                    <tr>
                                        <td data-label="Date">{{ match.end_time }}</td>
                                        <td data-label="Opponent">{{ opponent.username }}</td>
                                        <td data-label="Your Score">
                                            {% if match.user1_id == user.id %}
                                                {{ match.user1_score }}
                                            {% else %}
                                                {{ match.user2_score }}
                                            {% endif %}
                                        </td>
                                        <td data-label="Opponent Score">
                                            {% if match.user1_id == user.id %}
                                                {{ match.user2_score }}
                                            {% else %}
                                                {{ match.user1_score }}
                                            {% endif %}
                                        </td>
                                        <td data-label="Result">
                                            {% if match.winner_id == user.id %}
                                                <span class="badge bg-success">Win</span>
                                            {% elif match.winner_id == None %}
                                                <span class="badge bg-secondary">Draw</span>
                                            {% else %}
                                                <span class="badge bg-danger">Loss</span>
                                            {% endif %}
                                        </td>
                                        <td data-label="Actions">
                                            <a href="{{ url_for('battle.view_match_details', match_id=match.id) }}" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-eye me-1"></i>Details
                                            </a>
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p class="text-center">You haven't played any battles yet.</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
