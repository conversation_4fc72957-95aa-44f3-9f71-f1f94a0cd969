{% extends 'base.html' %}

{% block title %}Battle - JEE Battle{% endblock %}

{% block head %}
<style>
    /* Styling for clickable options */
    .option-container {
        cursor: pointer;
        padding: 8px;
        border-radius: 4px;
        transition: background-color 0.2s;
    }

    .option-container:hover {
        background-color: rgba(255, 255, 255, 0.1);
    }

    .option-container input[type="radio"]:checked + label {
        font-weight: bold;
        color: #0d6efd;
    }

    /* Hide the pre-match overlay initially */
    #pre-match-overlay {
        opacity: 1;
        transition: opacity 0.5s ease-in-out;
    }

    #pre-match-overlay.d-none {
        opacity: 0;
        pointer-events: none;
    }

    /* Animation for countdown */
    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.2); }
        100% { transform: scale(1); }
    }

    #countdown-number {
        animation: pulse 1s infinite;
    }
</style>
{% endblock %}

{% block content %}
<!-- Pre-match info overlay -->
<div id="pre-match-overlay" class="position-fixed top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center" style="background-color: rgba(0,0,0,0.9); z-index: 1050;">
    <div class="text-center text-white p-5 rounded" style="max-width: 500px; background-color: rgba(33,37,41,0.95);">
        <h2 class="mb-4">Battle Starting!</h2>

        <div id="match-info" class="mb-5">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div class="text-start">
                    <h4>{{ current_user.username }}</h4>
                    <p class="mb-0">Score: {{ current_user.score }}</p>
                </div>
                <div class="fs-1 fw-bold">VS</div>
                <div class="text-end">
                    <h4>{{ opponent.username }}</h4>
                    <p class="mb-0">Score: {{ opponent.score }}</p>
                </div>
            </div>

            <div class="alert alert-info">
                <ul class="mb-0 text-start">
                    <li>You will have <strong>{{ time_limit // 60 }} minutes {{ time_limit % 60 }} seconds</strong> to answer all questions</li>
                    <li>There are <strong>{{ questions|length }} questions</strong> in this battle</li>
                    <li>You can submit your answers at any time</li>
                    <li>Unanswered questions will be marked as not attempted</li>
                </ul>
            </div>
        </div>

        <div id="countdown" class="d-none">
            <h1 id="countdown-number" class="display-1 fw-bold text-danger">3</h1>
            <p>Get ready!</p>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h4 class="mb-0">Battle vs {{ opponent.username }}</h4>
                <div id="timer" class="badge bg-primary fs-5">{{ time_limit // 60 }}:{{ '%02d' % (time_limit % 60) }}</div>
            </div>
            <div class="card-body">
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="d-flex align-items-center">
                            <div class="me-3">
                                <span class="badge bg-primary fs-5">You</span>
                            </div>
                            <div>
                                <h5>{{ current_user.username }}</h5>
                                <p class="mb-0">Score: {{ current_user.score }}</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="d-flex align-items-center justify-content-end">
                            <div class="me-3">
                                <h5>{{ opponent.username }}</h5>
                                <p class="mb-0">Score: {{ opponent.score }}</p>
                            </div>
                            <div>
                                <span class="badge bg-secondary fs-5">Opponent</span>
                            </div>
                        </div>
                    </div>
                </div>

                <form id="battle-form" data-match-id="{{ match.id }}">
                    {% for question in questions %}
                        <div class="card mb-4 question-card" id="question-{{ question.id }}">
                            <div class="card-header">
                                <h5 class="mb-0">Question {{ loop.index }}</h5>
                            </div>
                            <div class="card-body">
                                <p class="question-text">{{ question.question }}</p>

                                <div class="options">
                                    <div class="form-check mb-2 option-container">
                                        <input class="form-check-input" type="radio" name="question-{{ question.id }}" id="option-{{ question.id }}-a" value="a"
                                            {% for answer in user_answers %}
                                                {% if answer.question_id == question.id and answer.answer == 'a' %}
                                                    checked
                                                {% endif %}
                                            {% endfor %}
                                        >
                                        <label class="form-check-label w-100" for="option-{{ question.id }}-a">
                                            A. {{ question.option_a }}
                                        </label>
                                    </div>
                                    <div class="form-check mb-2 option-container">
                                        <input class="form-check-input" type="radio" name="question-{{ question.id }}" id="option-{{ question.id }}-b" value="b"
                                            {% for answer in user_answers %}
                                                {% if answer.question_id == question.id and answer.answer == 'b' %}
                                                    checked
                                                {% endif %}
                                            {% endfor %}
                                        >
                                        <label class="form-check-label w-100" for="option-{{ question.id }}-b">
                                            B. {{ question.option_b }}
                                        </label>
                                    </div>
                                    <div class="form-check mb-2 option-container">
                                        <input class="form-check-input" type="radio" name="question-{{ question.id }}" id="option-{{ question.id }}-c" value="c"
                                            {% for answer in user_answers %}
                                                {% if answer.question_id == question.id and answer.answer == 'c' %}
                                                    checked
                                                {% endif %}
                                            {% endfor %}
                                        >
                                        <label class="form-check-label w-100" for="option-{{ question.id }}-c">
                                            C. {{ question.option_c }}
                                        </label>
                                    </div>
                                    <div class="form-check mb-2 option-container">
                                        <input class="form-check-input" type="radio" name="question-{{ question.id }}" id="option-{{ question.id }}-d" value="d"
                                            {% for answer in user_answers %}
                                                {% if answer.question_id == question.id and answer.answer == 'd' %}
                                                    checked
                                                {% endif %}
                                            {% endfor %}
                                        >
                                        <label class="form-check-label w-100" for="option-{{ question.id }}-d">
                                            D. {{ question.option_d }}
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    {% endfor %}

                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary btn-lg">Submit Answers</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const socket = io();
        const matchId = {{ match.id }};

        // Flag to track if we're intentionally redirecting
        window.isRedirecting = false;

        // Handle pre-match info and countdown
        const preMatchOverlay = document.getElementById('pre-match-overlay');
        const matchInfo = document.getElementById('match-info');
        const countdown = document.getElementById('countdown');
        const countdownNumber = document.getElementById('countdown-number');

        // Hide the main battle content initially
        document.querySelector('.row:not(#pre-match-overlay .row)').style.visibility = 'hidden';

        // Show pre-match info for 3 seconds only
        setTimeout(() => {
            matchInfo.classList.add('d-none');
            countdown.classList.remove('d-none');

            // Start countdown from 3
            let count = 3;
            countdownNumber.textContent = count;

            const countdownInterval = setInterval(() => {
                count--;
                countdownNumber.textContent = count;

                if (count <= 0) {
                    clearInterval(countdownInterval);
                    // Hide overlay and start the match
                    preMatchOverlay.classList.add('d-none');

                    // Show the battle content
                    document.querySelector('.row:not(#pre-match-overlay .row)').style.visibility = 'visible';

                    // Start the 15-second timer only after countdown completes
                    startBattleTimer();
                }
            }, 1000);
        }, 3000);

        // Make option containers clickable
        document.querySelectorAll('.option-container').forEach(container => {
            container.addEventListener('click', function(e) {
                // Find the radio input within this container
                const radio = this.querySelector('input[type="radio"]');
                if (radio && !radio.disabled) {
                    radio.checked = true;
                    // Create a change event to trigger any listeners
                    radio.dispatchEvent(new Event('change'));
                }
            });
        });

        // Join match room
        socket.on('connect', function() {
            console.log('Connected to Socket.IO');
            socket.emit('join_match', { match_id: matchId });
        });

        // Handle page unload/close - silently notify server without showing a dialog
        window.addEventListener('beforeunload', function(e) {
            // Only send the abandon request if we're not already redirecting to results
            if (!window.isRedirecting) {
                // Notify server that user is leaving the match
                fetch('/abandon-match/' + matchId, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    // Use keepalive to ensure the request completes even if the page is unloading
                    keepalive: true
                });
            }

            // Don't show a confirmation dialog
            // Intentionally not calling e.preventDefault() or setting e.returnValue
        });

        // Listen for user completed event
        socket.on('user_completed', function(data) {
            console.log('User completed:', data);
            if (data.user_id !== {{ current_user.id }}) {
                // Show notification that opponent has completed
                const alert = document.createElement('div');
                alert.className = 'alert alert-info';
                alert.textContent = 'Your opponent has submitted all answers!';
                document.querySelector('.card-body').prepend(alert);
            }
        });

        // Listen for match completed event
        socket.on('match_completed', function(data) {
            console.log('Match completed:', data);

            // Show notification that match is complete
            const alert = document.createElement('div');
            alert.className = 'alert alert-success';
            alert.textContent = 'Match complete! Redirecting to results...';
            document.querySelector('.card-body').prepend(alert);

            // Redirect to results page
            setTimeout(function() {
                window.isRedirecting = true;
                window.location.href = `/complete-match/${data.match_id}`;
            }, 1500);
        });

        // Listen for time expired event
        socket.on('time_expired', function(data) {
            console.log('Time expired:', data);

            // If this is from the other user, show notification and redirect
            if (data.user_id !== {{ current_user.id }}) {
                // Show notification that time is up
                const alert = document.createElement('div');
                alert.className = 'alert alert-warning';
                alert.textContent = 'Time is up! Redirecting to results...';
                document.querySelector('.card-body').prepend(alert);

                // Disable form
                const inputs = document.querySelectorAll('input[type="radio"]');
                inputs.forEach(input => input.disabled = true);
                const submitButton = document.querySelector('button[type="submit"]');
                if (submitButton) submitButton.disabled = true;

                // Redirect to results page
                setTimeout(function() {
                    window.isRedirecting = true;
                    window.location.href = `/complete-match/${data.match_id}`;
                }, 1500);
            }
        });

        // Listen for match abandoned event
        socket.on('match_abandoned', function(data) {
            console.log('Match abandoned:', data);

            // Show notification that the opponent abandoned the match
            const alert = document.createElement('div');
            alert.className = 'alert alert-success';
            alert.textContent = data.message || 'Your opponent has left the match! You win! Redirecting to results...';
            document.querySelector('.card-body').prepend(alert);

            // Disable form
            const inputs = document.querySelectorAll('input[type="radio"]');
            inputs.forEach(input => input.disabled = true);
            const submitButton = document.querySelector('button[type="submit"]');
            if (submitButton) submitButton.disabled = true;

            // Redirect to results page
            setTimeout(function() {
                window.isRedirecting = true;
                window.location.href = `/complete-match/${data.match_id}`;
            }, 1500);
        });

        // Timer functionality
        let timeLeft = {{ time_limit }};
        const timerElement = document.getElementById('timer');
        let timerInterval;

        // Function to format time as MM:SS
        function formatTime(seconds) {
            const minutes = Math.floor(seconds / 60);
            const remainingSeconds = seconds % 60;
            return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
        }

        // Make sure the timer shows the correct initial value
        timerElement.textContent = formatTime(timeLeft);

        // Function to start the battle timer
        function startBattleTimer() {
            // Reset timer to full time
            timeLeft = {{ time_limit }};
            timerElement.textContent = formatTime(timeLeft);

            // Clear any existing interval
            if (timerInterval) {
                clearInterval(timerInterval);
            }

            timerInterval = setInterval(function() {
                timeLeft--;
                timerElement.textContent = formatTime(timeLeft);

                // Calculate percentages of total time for color changes
                const totalTime = {{ time_limit }};
                const dangerThreshold = Math.min(60, totalTime * 0.1); // 10% of total time or 60 seconds, whichever is less
                const warningThreshold = Math.min(120, totalTime * 0.25); // 25% of total time or 120 seconds, whichever is less

                if (timeLeft <= dangerThreshold) {
                    timerElement.classList.add('bg-danger');
                    timerElement.classList.remove('bg-warning', 'bg-primary');
                } else if (timeLeft <= warningThreshold) {
                    timerElement.classList.add('bg-warning');
                    timerElement.classList.remove('bg-danger', 'bg-primary');
                } else {
                    timerElement.classList.add('bg-primary');
                    timerElement.classList.remove('bg-danger', 'bg-warning');
                }

                if (timeLeft <= 0) {
                    clearInterval(timerInterval);

                    // Show message that time is up
                    const alert = document.createElement('div');
                    alert.className = 'alert alert-warning';
                    alert.textContent = 'Time is up! Submitting your answers...';
                    document.querySelector('.card-body').prepend(alert);

                    // Disable form
                    const inputs = document.querySelectorAll('input[type="radio"]');
                    inputs.forEach(input => input.disabled = true);
                    const submitButton = document.querySelector('button[type="submit"]');
                    if (submitButton) submitButton.disabled = true;

                    // Submit all answers and notify both users
                    const questions = document.querySelectorAll('.question-card');
                    const promises = Array.from(questions).map(questionCard => {
                        const questionId = questionCard.id.split('-')[1];
                        const selectedOption = document.querySelector(`input[name="question-${questionId}"]:checked`);

                        if (selectedOption) {
                            const answer = selectedOption.value;
                            return fetch('/submit-answer', {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/x-www-form-urlencoded',
                                },
                                body: `match_id=${matchId}&question_id=${questionId}&answer=${answer}`
                            })
                            .then(response => response.json());
                        } else {
                            return fetch('/submit-answer', {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/x-www-form-urlencoded',
                                },
                                body: `match_id=${matchId}&question_id=${questionId}`
                            })
                            .then(response => response.json());
                        }
                    });

                    Promise.all(promises)
                        .then(() => {
                            // Emit a socket event to notify that time is up
                            socket.emit('time_up', { match_id: matchId, user_id: {{ current_user.id }} });

                            // Wait a moment before redirecting to ensure the event is sent
                            setTimeout(() => {
                                window.isRedirecting = true;
                                window.location.href = `/complete-match/${matchId}`;
                            }, 1000);
                        })
                        .catch(error => {
                            console.error('Error submitting answers:', error);
                        });
                }
            }, 1000);
        }

        // Note: We don't start the timer here - it will be started after the countdown

        // Handle form submission
        const form = document.getElementById('battle-form');
        form.addEventListener('submit', function(e) {
            e.preventDefault();

            // Don't clear the timer interval yet - let it run to 0
            // clearInterval(timerInterval);

            // Show message that answers are being submitted
            const submitAlert = document.createElement('div');
            submitAlert.className = 'alert alert-info';
            submitAlert.textContent = 'Submitting your answers...';
            document.querySelector('.card-body').prepend(submitAlert);

            // Disable form to prevent multiple submissions
            const inputs = document.querySelectorAll('input[type="radio"]');
            inputs.forEach(input => input.disabled = true);
            const submitButton = document.querySelector('button[type="submit"]');
            if (submitButton) submitButton.disabled = true;

            // Get all questions
            const questions = document.querySelectorAll('.question-card');
            let allSubmitted = true;

            // Submit each answer
            const promises = Array.from(questions).map(questionCard => {
                const questionId = questionCard.id.split('-')[1];
                const selectedOption = document.querySelector(`input[name="question-${questionId}"]:checked`);

                if (selectedOption) {
                    const answer = selectedOption.value;

                    return fetch('/submit-answer', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                        },
                        body: `match_id=${matchId}&question_id=${questionId}&answer=${answer}`
                    })
                    .then(response => response.json());
                } else {
                    // If no answer selected, still submit but with null answer
                    return fetch('/submit-answer', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                        },
                        body: `match_id=${matchId}&question_id=${questionId}`
                    })
                    .then(response => response.json());
                }
            });

            // After all answers are submitted
            Promise.all(promises)
                .then(results => {
                    console.log('All answers submitted:', results);

                    // Update the alert
                    submitAlert.className = 'alert alert-success';
                    submitAlert.textContent = 'Your answers have been submitted! Waiting for the timer to end...';

                    // Check if we need to redirect immediately
                    const lastResult = results[results.length - 1];
                    if (lastResult.redirect && timeLeft <= 0) {
                        window.isRedirecting = true;
                        window.location.href = lastResult.redirect;
                    } else {
                        // If timer is still running, wait for it to finish
                        // The timer will handle the redirect when it reaches 0

                        // If both users have submitted, we'll get a match_completed event
                        // which will handle the redirect
                    }
                })
                .catch(error => {
                    console.error('Error submitting answers:', error);
                    submitAlert.className = 'alert alert-danger';
                    submitAlert.textContent = 'There was an error submitting your answers. Please try again.';

                    // Re-enable the form
                    inputs.forEach(input => input.disabled = false);
                    if (submitButton) submitButton.disabled = false;
                });
        });
    });
</script>
{% endblock %}
