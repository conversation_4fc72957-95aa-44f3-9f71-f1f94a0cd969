{% extends 'base.html' %}

{% block title %}Payment Required - J<PERSON> Battle{% endblock %}

{% block head %}
<style>
    .payment-container {
        max-width: 600px;
        margin: 2rem auto;
    }

    .payment-card {
        border: none;
        border-radius: 12px;
        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.2);
        overflow: hidden;
    }

    .payment-header {
        background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
        padding: 1.5rem;
        text-align: center;
    }

    .payment-title {
        color: var(--white-pure);
        font-weight: 700;
        margin: 0;
        font-size: 1.75rem;
    }

    .payment-body {
        padding: 2rem;
        text-align: center;
    }

    .payment-icon {
        font-size: 3rem;
        color: #ff6b6b;
        margin-bottom: 1rem;
    }

    .qr-code-container {
        background: var(--white-pure);
        border: 2px solid var(--border-color);
        border-radius: 12px;
        padding: 1.5rem;
        margin: 1.5rem 0;
        display: inline-block;
    }

    .qr-code-image {
        width: 250px;
        height: auto;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .contact-info {
        background: var(--bg-secondary);
        border-radius: 8px;
        padding: 1.5rem;
        margin: 1.5rem 0;
        border-left: 4px solid var(--accent-primary);
    }

    .contact-number {
        font-size: 1.25rem;
        font-weight: 600;
        color: var(--accent-primary);
        margin: 0.5rem 0;
    }

    .user-email {
        background: var(--bg-tertiary);
        padding: 0.75rem 1rem;
        border-radius: 6px;
        font-family: monospace;
        font-size: 0.9rem;
        color: var(--text-secondary);
        margin: 0.5rem 0;
    }

    .payment-actions {
        margin-top: 2rem;
    }

    .btn-back {
        background: var(--bg-secondary);
        color: var(--text-primary);
        border: 1px solid var(--border-color);
    }

    .btn-back:hover {
        background: var(--bg-tertiary);
        color: var(--text-primary);
    }

    .payment-steps {
        text-align: left;
        margin: 1.5rem 0;
    }

    .payment-steps ol {
        padding-left: 1.5rem;
    }

    .payment-steps li {
        margin: 0.75rem 0;
        color: var(--text-secondary);
    }
</style>
{% endblock %}

{% block content %}
<div class="payment-container">
    <div class="payment-card">
        <div class="payment-header">
            <div class="payment-icon">
                <i class="fas fa-credit-card"></i>
            </div>
            <h2 class="payment-title">Payment Required</h2>
        </div>
        <div class="payment-body">
            <div class="alert alert-warning" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>Access Restricted:</strong> Please pay Rs 100 to access JEE Battle features.
            </div>

            <div class="text-center mb-4">
                <div class="h2 text-success mb-2">
                    <i class="fas fa-rupee-sign me-2"></i>100
                </div>
                <div class="text-muted">One-time payment for lifetime access</div>
            </div>

            <div class="qr-code-container">
                <img src="{{ url_for('static', filename='images/payment_qr.png') }}"
                     alt="Payment QR Code - Paytm UPI: 9484638348@ptsbi"
                     class="qr-code-image"
                     onerror="this.style.display='none'; document.getElementById('qr-fallback').style.display='block';">

                <!-- Fallback content if image doesn't load -->
                <div id="qr-fallback" style="display: none;" class="text-center p-4">
                    <i class="fas fa-qrcode fa-4x mb-3" style="color: #1f6feb;"></i>
                    <div class="h5 mb-2">Payment QR Code</div>
                    <div class="mb-2">
                        <strong>UPI ID:</strong> 9484638348@ptsbi
                    </div>
                    <div class="mb-2">
                        <strong>Name:</strong> Shah Vraj
                    </div>
                    <small class="text-muted">Use any UPI app to pay Rs 100 using the above UPI ID</small>
                </div>

                <div class="mt-2">
                    <small class="text-muted">
                        <i class="fas fa-mobile-alt me-1"></i>
                        Scan with any UPI app to pay Rs 100 • UPI ID: 9484638348@ptsbi
                    </small>
                </div>
            </div>

            <div class="contact-info">
                <h5><i class="fas fa-info-circle me-2"></i>Payment Instructions</h5>
                <div class="payment-steps">
                    <ol>
                        <li>Scan the QR code above to pay Rs 100</li>
                        <li>Complete the payment process</li>
                        <li>Take a screenshot of the payment confirmation</li>
                        <li>WhatsApp your email and screenshot to the number below</li>
                    </ol>
                </div>

                <div class="contact-number">
                    <i class="fab fa-whatsapp me-2"></i>WhatsApp: 9484638348
                </div>

                <div class="mt-3">
                    <a href="https://wa.me/919484638348?text=Hi%2C%20I%20have%20made%20payment%20of%20Rs%20100%20for%20JEE%20Battle.%20My%20email%3A%20{{ user_email }}%20%0A%0APlease%20find%20payment%20screenshot%20attached."
                       target="_blank"
                       class="btn btn-success btn-sm">
                        <i class="fab fa-whatsapp me-2"></i>Open WhatsApp
                    </a>
                </div>
                
                <div class="mt-3">
                    <strong>Your Email:</strong>
                    <div class="user-email">{{ user_email }}</div>
                </div>
                
                <div class="mt-3">
                    <small class="text-muted">
                        <i class="fas fa-clock me-1"></i>
                        After WhatsApp verification, your account will be activated within 24 hours.
                    </small>
                </div>
            </div>

            <div class="payment-actions">
                <a href="{{ url_for('auth.login') }}" class="btn btn-back me-2">
                    <i class="fas fa-arrow-left me-2"></i>Back to Login
                </a>
                <button type="button" class="btn btn-success" onclick="refreshPage()">
                    <i class="fas fa-sync-alt me-2"></i>I Have Paid - Check Status
                </button>
            </div>
        </div>
    </div>
</div>

<script>
function refreshPage() {
    // Clear the session and redirect to login to check payment status
    window.location.href = "{{ url_for('auth.login') }}";
}
</script>
{% endblock %}
