{% extends 'base.html' %}

{% block title %}Admin - User Statistics{% endblock %}

{% block head %}
<style>
    .stats-table {
        width: 100%;
        border-collapse: collapse;
    }

    .stats-table th {
        background-color: #1f6feb;
        color: white;
        padding: 12px;
        text-align: left;
        font-weight: 600;
    }

    .stats-table td {
        padding: 10px 12px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .stats-table tr:hover {
        background-color: rgba(255, 255, 255, 0.05);
    }

    .stats-card {
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
        margin-bottom: 2rem;
    }

    .stats-header {
        background: linear-gradient(135deg, #1f6feb 0%, #0d1117 100%);
        padding: 20px;
        color: white;
    }

    .stats-header h1 {
        margin: 0;
        font-size: 1.8rem;
    }

    .stats-body {
        padding: 0;
    }

    .badge-rating {
        background-color: #1f6feb;
        color: white;
        padding: 4px 8px;
        border-radius: 4px;
        font-weight: 600;
    }

    .badge-count {
        background-color: #238636;
        color: white;
        padding: 4px 8px;
        border-radius: 4px;
        font-weight: 600;
    }

    .badge-paid {
        background-color: #238636;
        color: white;
        padding: 4px 8px;
        border-radius: 4px;
        font-weight: 600;
    }

    .badge-unpaid {
        background-color: #da3633;
        color: white;
        padding: 4px 8px;
        border-radius: 4px;
        font-weight: 600;
    }

    .btn-payment {
        font-size: 0.75rem;
        padding: 4px 8px;
        margin: 2px;
    }

    /* Mobile responsive styles */
    @media (max-width: 768px) {
        .table-stack-mobile {
            border: 0;
        }

        .table-stack-mobile thead {
            display: none;
        }

        .table-stack-mobile tr {
            border: 1px solid rgba(255, 255, 255, 0.1);
            display: block;
            margin-bottom: 1rem;
            padding: 1rem;
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.02);
        }

        .table-stack-mobile td {
            border: none;
            display: block;
            text-align: right;
            padding: 0.5rem 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.05);
        }

        .table-stack-mobile td:before {
            content: attr(data-label) ": ";
            float: left;
            font-weight: bold;
            color: var(--accent-primary);
        }

        .table-stack-mobile td:last-child {
            border-bottom: none;
        }

        .d-flex.flex-column.flex-sm-row {
            flex-direction: column !important;
        }

        .btn-payment {
            width: 100%;
            margin: 2px 0;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="stats-card">
        <div class="stats-header">
            <h1>JEE Battle - User Statistics</h1>
            <p class="mb-0">Admin dashboard showing user statistics</p>
        </div>
        <div class="stats-body">
            <div class="table-responsive">
                <table class="stats-table table-stack-mobile">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>Username</th>
                            <th>Email</th>
                            <th>Rating</th>
                            <th>Matches Played</th>
                            <th>Matches Won</th>
                            <th>Matches Drawn</th>
                            <th>Matches Lost</th>
                            <th>Friends</th>
                            <th>Practice Sessions</th>
                            <th>Payment Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for user in user_stats %}
                            <tr>
                                <td data-label="ID">{{ user.id }}</td>
                                <td data-label="Username">{{ user.username }}</td>
                                <td data-label="Email">{{ user.email }}</td>
                                <td data-label="Rating"><span class="badge-rating">{{ user.score }}</span></td>
                                <td data-label="Matches Played">{{ user.matches_played }}</td>
                                <td data-label="Matches Won">{{ user.matches_won }}</td>
                                <td data-label="Matches Drawn">{{ user.matches_drawn }}</td>
                                <td data-label="Matches Lost">{{ user.matches_lost }}</td>
                                <td data-label="Friends"><span class="badge-count">{{ user.friend_count }}</span></td>
                                <td data-label="Practice Sessions"><span class="badge-count">{{ user.practice_count }}</span></td>
                                <td data-label="Payment Status">
                                    {% if user.has_paid %}
                                        <span class="badge-paid">
                                            <i class="fas fa-check-circle me-1"></i>Paid
                                        </span>
                                    {% else %}
                                        <span class="badge-unpaid">
                                            <i class="fas fa-times-circle me-1"></i>Unpaid
                                        </span>
                                    {% endif %}
                                </td>
                                <td data-label="Actions">
                                    <div class="d-flex flex-column flex-sm-row gap-1">
                                        <a href="{{ url_for('auth.admin_login', user_id=user.id) }}" class="btn btn-sm btn-primary btn-payment">
                                            <i class="fas fa-sign-in-alt me-1"></i> Login
                                        </a>
                                        {% if user.has_paid %}
                                            <a href="{{ url_for('main.admin_toggle_payment', user_id=user.id) }}"
                                               class="btn btn-sm btn-warning btn-payment"
                                               onclick="return confirm('Are you sure you want to deactivate payment for {{ user.username }}?')">
                                                <i class="fas fa-ban me-1"></i> Deactivate
                                            </a>
                                        {% else %}
                                            <a href="{{ url_for('main.admin_toggle_payment', user_id=user.id) }}"
                                               class="btn btn-sm btn-success btn-payment"
                                               onclick="return confirm('Are you sure you want to activate payment for {{ user.username }}?')">
                                                <i class="fas fa-check me-1"></i> Activate
                                            </a>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}
