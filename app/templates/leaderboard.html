{% extends 'base.html' %}

{% block title %}Leaderboard - JEE Battle{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ url_for('main.dashboard') }}">Dashboard</a></li>
                <li class="breadcrumb-item active" aria-current="page">Leaderboard</li>
            </ol>
        </nav>
    </div>
</div>

<div class="row">
    <div class="col-md-4">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Your Ranking</h5>
            </div>
            <div class="card-body text-center">
                <h1 class="display-1">{{ user_rank }}</h1>
                <p class="lead">Your current rank</p>
                <hr>
                <div class="d-flex justify-content-between mb-2">
                    <strong>Score:</strong>
                    <span>{{ user.score }}</span>
                </div>
                <div class="d-flex justify-content-between mb-2">
                    <strong>Matches Played:</strong>
                    <span>{{ user.matches_played }}</span>
                </div>
                <div class="d-flex justify-content-between mb-2">
                    <strong>Matches Won:</strong>
                    <span>{{ user.matches_won }}</span>
                </div>
                <div class="d-flex justify-content-between mb-2">
                    <strong>Win Rate:</strong>
                    <span>
                        {% if user.matches_played > 0 %}
                            {{ (user.matches_won / user.matches_played * 100) | round(1) }}%
                        {% else %}
                            0%
                        {% endif %}
                    </span>
                </div>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Start a Battle</h5>
            </div>
            <div class="card-body text-center">
                {% if active_match %}
                    <p>You have an active battle!</p>
                    <a href="{{ url_for('battle.battle_room', match_id=active_match.id) }}" class="btn btn-primary btn-lg">Continue Battle</a>
                {% else %}
                    <p>Ready to test your JEE knowledge?</p>
                    <a href="{{ url_for('battle.start_battle') }}" class="btn btn-primary btn-lg">Start Battle</a>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="col-md-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Top Players</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover table-stack-mobile">
                        <thead>
                            <tr>
                                <th>Rank</th>
                                <th>Username</th>
                                <th>Score</th>
                                <th>Matches</th>
                                <th>Wins</th>
                                <th>Win Rate</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for top_user in top_users %}
                                <tr {% if top_user.id == user.id %}class="table-primary"{% endif %}>
                                    <td data-label="Rank">{{ loop.index }}</td>
                                    <td data-label="Username">{{ top_user.username }}</td>
                                    <td data-label="Score">{{ top_user.score }}</td>
                                    <td data-label="Matches">{{ top_user.matches_played }}</td>
                                    <td data-label="Wins">{{ top_user.matches_won }}</td>
                                    <td data-label="Win Rate">
                                        {% if top_user.matches_played > 0 %}
                                            {{ (top_user.matches_won / top_user.matches_played * 100) | round(1) }}%
                                        {% else %}
                                            0%
                                        {% endif %}
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
