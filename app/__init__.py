from flask import Flask
from flask_socketio import Socket<PERSON>
from flask_login import LoginManager

socketio = SocketIO()
login_manager = LoginManager()

def create_app(config=None):
    app = Flask(__name__)
    app.config['SECRET_KEY'] = 'jee_battle_secret_key_2025'  # Hardcoded secret key

    DB_PATH = './battle_app.db'

    app.config['SQLALCHEMY_DATABASE_URI'] = f'sqlite:///{DB_PATH}'

    # Set default battle configuration
    app.config['QUESTIONS_PER_BATTLE'] = 3
    app.config['BATTLE_DURATION_SECONDS'] = 15

    # Update with custom configuration if provided
    if config:
        for key, value in config.items():
            app.config[key] = value

    from app.models.user import User
    from app.models.question import Question
    from app.models.match import Match
    from app.models.user_answer import UserAnswer

    from app.routes.auth import auth_bp
    from app.routes.main import main_bp
    from app.routes.battle import battle_bp
    from app.routes.friends import friends_bp

    app.register_blueprint(auth_bp)
    app.register_blueprint(main_bp)
    app.register_blueprint(battle_bp)
    app.register_blueprint(friends_bp)

    login_manager.init_app(app)
    socketio.init_app(app)

    @login_manager.user_loader
    def load_user(user_id):
        return User.get_by_id(int(user_id))

    with app.app_context():
        from app.models.db import init_db, fix_matches_played, fix_rating_history
        init_db()

        # Load questions from JSON if the table is empty
        from app.models.question import load_questions_from_json
        load_questions_from_json()

    return app
