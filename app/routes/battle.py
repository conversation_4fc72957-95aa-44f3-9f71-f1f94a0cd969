from flask import Blueprint, render_template, redirect, url_for, flash, request, jsonify
from flask_login import login_required, current_user
from flask_socketio import emit, join_room, leave_room
from app import socketio
from app.models.user import User
from app.models.match import Match
from app.models.question import Question
from app.models.user_answer import UserAnswer
import random
import math
from datetime import datetime

battle_bp = Blueprint('battle', __name__)

# Dictionary to store users waiting for a match
waiting_users = {}

# Dictionary to store the last time a user checked for a match
last_check_time = {}

# Import threading for locks
import threading

# Create a lock for the waiting_users dictionary
waiting_users_lock = threading.RLock()

@battle_bp.route('/start-battle')
@login_required
def start_battle():
    # Check if user is already in an active match
    active_match = Match.get_active_match_for_user(current_user.id)
    if active_match:
        # If match is abandoned, don't allow resuming
        if active_match.status == 'abandoned':
            flash('Your previous match was abandoned. You cannot resume it.', 'warning')
            return redirect(url_for('main.dashboard'))

        # If match is still active, redirect to it
        return redirect(url_for('battle.battle_room', match_id=active_match.id))

    # Add user to waiting list with thread safety
    with waiting_users_lock:
        # Double-check if user is already in an active match
        active_match = Match.get_active_match_for_user(current_user.id)
        if active_match:
            return redirect(url_for('battle.battle_room', match_id=active_match.id))

        waiting_users[current_user.id] = current_user.score
        # Initialize last check time
        last_check_time[current_user.id] = datetime.now()

    # Always redirect to waiting room first (enforcing at least 1 second wait)
    return redirect(url_for('battle.waiting_room'))

@battle_bp.route('/waiting-room')
@login_required
def waiting_room():
    # Check if user is already in an active match
    active_match = Match.get_active_match_for_user(current_user.id)
    if active_match:
        # Clean up waiting list if user is already matched
        with waiting_users_lock:
            if current_user.id in waiting_users:
                del waiting_users[current_user.id]
            if current_user.id in last_check_time:
                del last_check_time[current_user.id]
        return redirect(url_for('battle.battle_room', match_id=active_match.id))

    # Make sure user is in waiting list with thread safety
    with waiting_users_lock:
        if current_user.id not in waiting_users:
            waiting_users[current_user.id] = current_user.score
            last_check_time[current_user.id] = datetime.now()

    return render_template('battle/waiting.html')

@battle_bp.route('/cancel-waiting')
@login_required
def cancel_waiting():
    # Remove user from waiting list with thread safety
    with waiting_users_lock:
        if current_user.id in waiting_users:
            del waiting_users[current_user.id]
        if current_user.id in last_check_time:
            del last_check_time[current_user.id]

    flash('Battle search cancelled', 'info')
    return redirect(url_for('main.dashboard'))

@battle_bp.route('/battle-room/<int:match_id>')
@login_required
def battle_room(match_id):
    # Clean up waiting list for this user
    with waiting_users_lock:
        if current_user.id in waiting_users:
            del waiting_users[current_user.id]
        if current_user.id in last_check_time:
            del last_check_time[current_user.id]

    match = Match.get_by_id(match_id)

    if not match:
        flash('Match not found', 'danger')
        return redirect(url_for('main.dashboard'))

    # Check if user is part of this match
    if match.user1_id != current_user.id and match.user2_id != current_user.id:
        flash('You are not part of this match', 'danger')
        return redirect(url_for('main.dashboard'))

    # If match is already completed, show results
    if match.status == 'completed':
        return redirect(url_for('battle.complete_match', match_id=match_id))

    # If match is abandoned, don't allow resuming
    if match.status == 'abandoned':
        flash('This match was abandoned and cannot be resumed', 'warning')
        return redirect(url_for('main.dashboard'))

    # Update match status if it's still pending
    if match.status == 'pending':
        match.update_status('in_progress')

    # Get opponent
    opponent_id = match.user2_id if match.user1_id == current_user.id else match.user1_id
    opponent = User.get_by_id(opponent_id)

    # Get questions for this match
    questions = match.get_questions()

    # Get user's answers
    user_answers = UserAnswer.get_user_match_answers(match_id, current_user.id)

    # Create empty answers for questions that haven't been answered yet
    question_ids = [q.id for q in questions]
    answered_question_ids = [a.question_id for a in user_answers]

    for q_id in question_ids:
        if q_id not in answered_question_ids:
            UserAnswer.create(match_id, current_user.id, q_id)

    # Refresh user answers after creating empty ones
    user_answers = UserAnswer.get_user_match_answers(match_id, current_user.id)

    # Check if both users have submitted all their answers
    user1_done = UserAnswer.has_user_answered_all(match_id, match.user1_id)
    user2_done = UserAnswer.has_user_answered_all(match_id, match.user2_id)

    # If both users are done, calculate scores and complete the match
    if user1_done and user2_done and match.status == 'in_progress':
        # Calculate scores
        user1_score = UserAnswer.get_user_score(match_id, match.user1_id)
        user2_score = UserAnswer.get_user_score(match_id, match.user2_id)

        # Determine winner
        winner_id = None
        if user1_score > user2_score:
            winner_id = match.user1_id
        elif user2_score > user1_score:
            winner_id = match.user2_id

        # Update match with scores and winner
        match.complete_match(user1_score, user2_score, winner_id)

        # Update user stats
        user1 = User.get_by_id(match.user1_id)
        user2 = User.get_by_id(match.user2_id)

        user1.increment_matches_played()
        user2.increment_matches_played()

        if winner_id:
            if winner_id == user1.id:
                user1.increment_matches_won()
            else:
                user2.increment_matches_won()

        # Update ELO ratings
        update_elo_ratings(user1, user2, user1_score, user2_score, match_id)

        # Notify both users that the match is complete
        socketio.emit('match_completed',
                     {'match_id': match_id},
                     room=f'match_{match_id}')

        return redirect(url_for('battle.complete_match', match_id=match_id))

    # Use the configured battle duration
    # This ensures both users get the full time
    from flask import current_app
    time_limit = current_app.config.get('BATTLE_DURATION_SECONDS', 15)

    return render_template('battle/battle.html',
                          match=match,
                          opponent=opponent,
                          questions=questions,
                          user_answers=user_answers,
                          time_limit=time_limit)

@battle_bp.route('/submit-answer', methods=['POST'])
@login_required
def submit_answer():
    match_id = request.form.get('match_id', type=int)
    question_id = request.form.get('question_id', type=int)
    answer = request.form.get('answer')

    if not match_id or not question_id:
        return jsonify({'success': False, 'message': 'Invalid request'})

    match = Match.get_by_id(match_id)

    if not match:
        return jsonify({'success': False, 'message': 'Match not found'})

    # Check if user is part of this match
    if match.user1_id != current_user.id and match.user2_id != current_user.id:
        return jsonify({'success': False, 'message': 'You are not part of this match'})

    # Update or create the answer
    UserAnswer.update_answer(match_id, current_user.id, question_id, answer)

    # Check if both users have submitted all their answers
    user1_done = UserAnswer.has_user_answered_all(match_id, match.user1_id)
    user2_done = UserAnswer.has_user_answered_all(match_id, match.user2_id)

    # If both users are done, calculate scores and complete the match
    if user1_done and user2_done:
        # Notify other user that this user has completed all questions
        socketio.emit('user_completed',
                     {'user_id': current_user.id, 'match_id': match_id},
                     room=f'match_{match_id}')

        # If match is still in progress, complete it
        if match.status == 'in_progress':
            # Calculate scores
            user1_score = UserAnswer.get_user_score(match_id, match.user1_id)
            user2_score = UserAnswer.get_user_score(match_id, match.user2_id)

            # Determine winner
            winner_id = None
            if user1_score > user2_score:
                winner_id = match.user1_id
            elif user2_score > user1_score:
                winner_id = match.user2_id

            # Update match with scores and winner
            match.complete_match(user1_score, user2_score, winner_id)

            # Update user stats
            user1 = User.get_by_id(match.user1_id)
            user2 = User.get_by_id(match.user2_id)

            user1.increment_matches_played()
            user2.increment_matches_played()

            if winner_id:
                if winner_id == user1.id:
                    user1.increment_matches_won()
                else:
                    user2.increment_matches_won()

            # Update ELO ratings
            update_elo_ratings(user1, user2, user1_score, user2_score, match_id)

            # Notify both users that the match is complete
            socketio.emit('match_completed',
                         {'match_id': match_id},
                         room=f'match_{match_id}')

        return jsonify({
            'success': True,
            'message': 'Answer submitted',
            'redirect': url_for('battle.complete_match', match_id=match_id)
        })

    return jsonify({'success': True, 'message': 'Answer submitted'})

@battle_bp.route('/complete-match/<int:match_id>')
@login_required
def complete_match(match_id):
    match = Match.get_by_id(match_id)

    if not match:
        flash('Match not found', 'danger')
        return redirect(url_for('main.dashboard'))

    # Check if user is part of this match
    if match.user1_id != current_user.id and match.user2_id != current_user.id:
        flash('You are not part of this match', 'danger')
        return redirect(url_for('main.dashboard'))

    # If match is already completed, just show the results
    if match.status == 'completed':
        user1 = User.get_by_id(match.user1_id)
        user2 = User.get_by_id(match.user2_id)

        return render_template('battle/results.html',
                              match=match,
                              user1=user1,
                              user2=user2)

    # Calculate scores
    user1_score = UserAnswer.get_user_score(match_id, match.user1_id)
    user2_score = UserAnswer.get_user_score(match_id, match.user2_id)

    # Determine winner
    winner_id = None
    if user1_score > user2_score:
        winner_id = match.user1_id
    elif user2_score > user1_score:
        winner_id = match.user2_id

    # Update match with scores and winner
    match.complete_match(user1_score, user2_score, winner_id)

    # Update user stats
    user1 = User.get_by_id(match.user1_id)
    user2 = User.get_by_id(match.user2_id)

    user1.increment_matches_played()
    user2.increment_matches_played()

    if winner_id:
        if winner_id == user1.id:
            user1.increment_matches_won()
        else:
            user2.increment_matches_won()

    # Update ELO ratings
    update_elo_ratings(user1, user2, user1_score, user2_score, match_id)

    return render_template('battle/results.html',
                          match=match,
                          user1=user1,
                          user2=user2)

@battle_bp.route('/check-match-status')
@login_required
def check_match_status():
    # Update last check time with thread safety
    with waiting_users_lock:
        # Update last check time even if not in waiting list
        # This helps with tracking active users
        last_check_time[current_user.id] = datetime.now()

        # Check if user is in waiting list
        if current_user.id not in waiting_users:
            # Check if user has an active match anyway
            active_match = Match.get_active_match_for_user(current_user.id)
            if active_match:
                return jsonify({
                    'status': 'matched',
                    'match_id': active_match.id,
                    'redirect': url_for('battle.battle_room', match_id=active_match.id)
                })
            return jsonify({'status': 'not_waiting'})

    # Check if user has been matched
    active_match = Match.get_active_match_for_user(current_user.id)
    if active_match:
        # Don't remove from waiting list here - we'll do that in battle_room
        # This ensures both users get redirected to the match
        return jsonify({
            'status': 'matched',
            'match_id': active_match.id,
            'redirect': url_for('battle.battle_room', match_id=active_match.id)
        })

    # Try to find a match after the user has waited
    match_id = find_match(current_user.id)
    if match_id:
        return jsonify({
            'status': 'matched',
            'match_id': match_id,
            'redirect': url_for('battle.battle_room', match_id=match_id)
        })

    # Clean up stale users (those who haven't checked in for more than 30 seconds)
    cleanup_stale_users()

    return jsonify({'status': 'waiting'})

# Helper function to find a match for a user
def find_match(user_id):
    # Use thread safety for the entire operation
    with waiting_users_lock:
        if user_id not in waiting_users:
            return None

        # Double-check if user is already in a match
        active_match = Match.get_active_match_for_user(user_id)
        if active_match:
            # Don't remove from waiting list here - we'll keep them in the list
            # until they're redirected to the match
            return active_match.id

        user_score = waiting_users[user_id]
        best_match = None
        min_score_diff = float('inf')

        # Find the closest score match
        for other_id, other_score in list(waiting_users.items()):
            if other_id != user_id:
                # Check if other user is already in a match
                other_active_match = Match.get_active_match_for_user(other_id)
                if other_active_match:
                    # Don't remove from waiting list here - we'll keep them in the list
                    # until they're redirected to the match
                    continue

                score_diff = abs(user_score - other_score)
                if score_diff < min_score_diff:
                    min_score_diff = score_diff
                    best_match = other_id

        if best_match:
            # Create a new match with transaction safety
            match = Match.create_with_players(user_id, best_match)

            if match:
                # Mark both users as matched but don't remove them from waiting list yet
                # We'll keep them in the list until they're redirected to the match
                # This way, if one user gets redirected but the other doesn't check in yet,
                # they'll still be matched when they do check in

                # Notify the other user that a match has been found
                socketio.emit('match_found',
                            {'match_id': match.id},
                            room=f'user_{best_match}')

                return match.id

    return None

# Helper function to clean up stale users
def cleanup_stale_users():
    now = datetime.now()
    stale_threshold = 30  # seconds

    with waiting_users_lock:
        # Find users who haven't checked in for a while
        stale_users = []
        for user_id in list(last_check_time.keys()):
            last_time = last_check_time[user_id]
            if (now - last_time).total_seconds() > stale_threshold:
                # Check if user is in an active match before marking as stale
                active_match = Match.get_active_match_for_user(user_id)
                if not active_match:
                    stale_users.append(user_id)

        # Remove stale users
        for user_id in stale_users:
            if user_id in waiting_users:
                del waiting_users[user_id]
            if user_id in last_check_time:
                del last_check_time[user_id]

# Import the RatingHistory model
from app.models.rating_history import RatingHistory

# Helper function to update scores based on match results
def update_elo_ratings(user1, user2, user1_score, user2_score, match_id=None):
    # Determine the winner
    if user1_score > user2_score:
        # User1 wins
        winner = user1
        loser = user2
    elif user2_score > user1_score:
        # User2 wins
        winner = user2
        loser = user1
    else:
        # It's a draw - use ELO calculation
        # Calculate expected scores
        expected_score1 = 1 / (1 + 10 ** ((user2.score - user1.score) / 400))
        expected_score2 = 1 / (1 + 10 ** ((user1.score - user2.score) / 400))

        # Calculate actual scores (normalized to be between 0 and 1)
        from flask import current_app
        total_questions = current_app.config.get('QUESTIONS_PER_BATTLE', 3)
        actual_score1 = user1_score / total_questions
        actual_score2 = user2_score / total_questions

        # Calculate new ratings
        k_factor = 32  # Standard K-factor for ELO
        new_rating1 = user1.score + k_factor * (actual_score1 - expected_score1)
        new_rating2 = user2.score + k_factor * (actual_score2 - expected_score2)

        # Store old ratings
        old_rating1 = user1.score
        old_rating2 = user2.score

        # Update user ratings
        user1.update_score(round(new_rating1))
        user2.update_score(round(new_rating2))

        # Record rating history - match_id should always be provided
        # This ensures we have rating history for all matches
        if match_id:
            RatingHistory.create(user1.id, old_rating1, user1.score, match_id)
            RatingHistory.create(user2.id, old_rating2, user2.score, match_id)
        else:
            print("Warning: match_id not provided for rating history in draw scenario")

        return

    # For win/loss scenario
    # Calculate score difference and rating difference
    score_diff = abs(user1_score - user2_score)
    rating_diff = abs(winner.score - loser.score)

    # Base points for winning/losing
    base_win_points = 30
    base_lose_points = 20

    # Adjust points based on rating difference
    if winner.score > loser.score:
        # Higher rated player beating lower rated player - less points
        win_points = max(10, base_win_points - (rating_diff // 50))
        lose_points = min(30, base_lose_points + (rating_diff // 100))
    else:
        # Lower rated player beating higher rated player - more points
        win_points = min(50, base_win_points + (rating_diff // 50))
        lose_points = max(10, base_lose_points - (rating_diff // 100))

    # Adjust based on score difference
    win_points += min(10, score_diff * 3)

    # Store old ratings
    old_winner_rating = winner.score
    old_loser_rating = loser.score

    # Update scores
    winner.update_score(winner.score + win_points)
    loser.update_score(loser.score - lose_points)

    # Record rating history - match_id should always be provided
    # This ensures we have rating history for all matches
    if match_id:
        RatingHistory.create(winner.id, old_winner_rating, winner.score, match_id)
        RatingHistory.create(loser.id, old_loser_rating, loser.score, match_id)
    else:
        print("Warning: match_id not provided for rating history in win/loss scenario")

# Socket.IO event handlers
@socketio.on('connect')
def handle_connect():
    if current_user.is_authenticated:
        join_room(f'user_{current_user.id}')

@socketio.on('join_match')
def handle_join_match(data):
    match_id = data.get('match_id')
    if match_id:
        join_room(f'match_{match_id}')

@socketio.on('leave_match')
def handle_leave_match(data):
    match_id = data.get('match_id')
    if match_id:
        leave_room(f'match_{match_id}')

@socketio.on('time_up')
def handle_time_up(data):
    match_id = data.get('match_id')
    user_id = data.get('user_id')

    if match_id:
        # Notify all users in the match that time is up
        socketio.emit('time_expired',
                     {'match_id': match_id, 'user_id': user_id},
                     room=f'match_{match_id}')

@battle_bp.route('/abandon-match/<int:match_id>', methods=['POST'])
@login_required
def abandon_match(match_id):
    match = Match.get_by_id(match_id)

    if not match:
        return jsonify({'success': False, 'message': 'Match not found'})

    # Check if user is part of this match
    if match.user1_id != current_user.id and match.user2_id != current_user.id:
        return jsonify({'success': False, 'message': 'You are not part of this match'})

    # Only abandon if match is still in progress
    if match.status == 'in_progress':
        # Determine the winner (the user who didn't leave)
        winner_id = match.user2_id if match.user1_id == current_user.id else match.user1_id

        # Mark match as abandoned with the current user as the one who left
        match.abandon_match(abandoning_user_id=current_user.id)

        # Update user stats
        user1 = User.get_by_id(match.user1_id)
        user2 = User.get_by_id(match.user2_id)

        user1.increment_matches_played()
        user2.increment_matches_played()

        # Increment matches won for the winner (the user who didn't leave)
        if winner_id == user1.id:
            user1.increment_matches_won()
            winner = user1
            loser = user2
        else:
            user2.increment_matches_won()
            winner = user2
            loser = user1

        # For abandonment, we give a fixed rating change instead of using the ELO calculation
        # The winner gets points, the loser (who abandoned) loses points
        win_points = 30  # Fixed points for winning due to opponent abandonment
        lose_points = 20  # Fixed points for losing due to abandonment

        # Store old ratings
        old_winner_rating = winner.score
        old_loser_rating = loser.score

        # Update scores
        winner.update_score(winner.score + win_points)
        loser.update_score(loser.score - lose_points)

        # Record rating history
        RatingHistory.create(winner.id, old_winner_rating, winner.score, match_id)
        RatingHistory.create(loser.id, old_loser_rating, loser.score, match_id)

        # Notify the other user that the match was abandoned and they won
        other_user_id = match.user2_id if match.user1_id == current_user.id else match.user1_id
        socketio.emit('match_abandoned',
                     {
                         'match_id': match_id,
                         'user_id': current_user.id,
                         'winner_id': winner_id,
                         'message': f'Your opponent left the match. You win! Your rating increased by {win_points} points.'
                     },
                     room=f'user_{other_user_id}')

    return jsonify({'success': True})

@battle_bp.route('/match-details/<int:match_id>')
@login_required
def view_match_details(match_id):
    match = Match.get_by_id(match_id)

    if not match:
        flash('Match not found', 'danger')
        return redirect(url_for('main.dashboard'))

    # Check if user is part of this match
    if match.user1_id != current_user.id and match.user2_id != current_user.id:
        flash('You are not part of this match', 'danger')
        return redirect(url_for('main.dashboard'))

    # Get opponent
    opponent_id = match.user2_id if match.user1_id == current_user.id else match.user1_id
    opponent = User.get_by_id(opponent_id)

    # Get questions for this match
    questions = match.get_questions()

    # Get user's answers
    user_answers_list = UserAnswer.get_user_match_answers(match_id, current_user.id)

    # Get opponent's answers
    opponent_answers_list = UserAnswer.get_user_match_answers(match_id, opponent_id)

    # Create dictionaries for quick lookup by question_id
    user_answers = {answer.question_id: answer for answer in user_answers_list}
    opponent_answers = {answer.question_id: answer for answer in opponent_answers_list}

    return render_template('battle/match_details.html',
                          match=match,
                          opponent=opponent,
                          questions=questions,
                          user_answers=user_answers,
                          opponent_answers=opponent_answers)
