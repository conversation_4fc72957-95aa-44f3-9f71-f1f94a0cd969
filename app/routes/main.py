from flask import Blueprint, render_template, redirect, url_for, flash, request, jsonify
from flask_login import login_required, current_user
from app.models.match import Match
from app.models.user import User
from app.models.question import Question
from app.models.practice_result import PracticeResult
from app.models.rating_history import RatingHistory
from app.models.db import get_db
import json
import os
import random
import datetime

main_bp = Blueprint('main', __name__)

@main_bp.route('/')
def index():
    if current_user.is_authenticated:
        return redirect(url_for('main.dashboard'))
    return render_template('index.html')

@main_bp.route('/dashboard')
@login_required
def dashboard():
    # Get only recent matches for the dashboard (limited to 5)
    recent_matches = Match.get_user_matches(current_user.id, limit=5)

    # Check if user is already in an active match
    active_match = Match.get_active_match_for_user(current_user.id)

    # Get opponent usernames for each match
    match_data = []
    for match in recent_matches:
        # Get opponent
        opponent_id = match.user2_id if match.user1_id == current_user.id else match.user1_id
        opponent = User.get_by_id(opponent_id)

        match_data.append({
            'match': match,
            'opponent': opponent
        })

    # Get recent matches for the user
    recent_matches = Match.get_all_user_matches(current_user.id)

    # Get rating history for the graph
    rating_history = RatingHistory.get_by_user_id(current_user.id)

    # Create a dictionary of rating history by match_id
    rating_by_match = {}
    if rating_history:
        for record in rating_history:
            rating_by_match[record.match_id] = record.new_rating

    # Format rating history for the chart
    rating_labels = []
    rating_data = []

    # Always start with initial rating of 1000
    rating_labels.append("Initial")
    rating_data.append(1000)

    # Current rating to track progression
    current_rating = 1000

    # Process matches in chronological order (oldest first)
    chronological_matches = list(reversed(recent_matches))

    # Add each match with sequential numbering
    for i, match in enumerate(chronological_matches):
        match_id = match.id

        # If we have rating history for this match, use it
        if match_id in rating_by_match:
            current_rating = rating_by_match[match_id]

        # Add the match to the chart
        rating_labels.append(f"Match {i+1}")
        rating_data.append(current_rating)

    return render_template('dashboard.html',
                          user=current_user,
                          match_data=match_data,
                          active_match=active_match,
                          rating_labels=rating_labels,
                          rating_data=rating_data)

@main_bp.route('/vraj_only')
def vraj_only():
    """Admin endpoint to view user statistics"""
    # Get all users
    from app.models.db import get_db
    db = get_db()

    # Get all users with their ratings, matches played, matches won, and payment status
    users = db.execute(
        'SELECT id, username, email, score, matches_played, matches_won, has_paid FROM users'
    ).fetchall()

    # Get friend counts for each user
    from app.models.friend import Friendship
    user_stats = []

    for user in users:
        # Get friend count
        friend_count = len(Friendship.get_friend_ids(user['id']))

        # Get practice session count
        practice_count = db.execute(
            'SELECT COUNT(*) FROM practice_results WHERE user_id = ?',
            (user['id'],)
        ).fetchone()[0]

        # Calculate total completed matches directly from the database
        total_completed_matches = db.execute(
            '''SELECT COUNT(*) FROM matches
               WHERE (user1_id = ? OR user2_id = ?) AND status = 'completed' ''',
            (user['id'], user['id'])
        ).fetchone()[0]

        # Count matches won (where user is the winner)
        matches_won = db.execute(
            '''SELECT COUNT(*) FROM matches
               WHERE winner_id = ? AND status = 'completed' ''',
            (user['id'],)
        ).fetchone()[0]

        # Count drawn matches (where winner_id is NULL)
        matches_drawn = db.execute(
            '''SELECT COUNT(*) FROM matches
               WHERE (user1_id = ? OR user2_id = ?) AND status = 'completed' AND winner_id IS NULL''',
            (user['id'], user['id'])
        ).fetchone()[0]

        # Calculate matches lost (total - won - drawn)
        matches_lost = total_completed_matches - matches_won - matches_drawn

        # Add to user stats
        user_stats.append({
            'id': user['id'],
            'username': user['username'],
            'email': user['email'],
            'score': user['score'],
            'matches_played': total_completed_matches,
            'matches_won': matches_won,
            'matches_drawn': matches_drawn,
            'matches_lost': matches_lost,
            'friend_count': friend_count,
            'practice_count': practice_count,
            'has_paid': bool(user['has_paid']) if 'has_paid' in user.keys() else False
        })

    return render_template('admin/user_stats.html', user_stats=user_stats)

@main_bp.route('/admin/toggle-payment/<int:user_id>')
def admin_toggle_payment(user_id):
    """Admin function to toggle user payment status"""
    # Get the user
    user = User.get_by_id(user_id)

    if not user:
        flash('User not found', 'danger')
        return redirect(url_for('main.vraj_only'))

    # Toggle payment status
    new_payment_status = not user.has_paid
    user.update_payment_status(new_payment_status)

    status_text = "activated" if new_payment_status else "deactivated"
    flash(f'Payment status {status_text} for user {user.username}', 'success')

    return redirect(url_for('main.vraj_only'))

@main_bp.route('/all-matches')
@login_required
def all_matches():
    # Get all user's matches
    all_matches = Match.get_all_user_matches(current_user.id)

    # Check if user is already in an active match
    active_match = Match.get_active_match_for_user(current_user.id)

    # Get opponent usernames for each match
    match_data = []
    for match in all_matches:
        # Get opponent
        opponent_id = match.user2_id if match.user1_id == current_user.id else match.user1_id
        opponent = User.get_by_id(opponent_id)

        match_data.append({
            'match': match,
            'opponent': opponent
        })

    return render_template('all_matches.html',
                          user=current_user,
                          match_data=match_data,
                          active_match=active_match)

@main_bp.route('/leaderboard')
@login_required
def leaderboard():
    # Get top users by score
    top_users = User.get_top_users(20)  # Get top 20 users

    # Check if user is already in an active match
    active_match = Match.get_active_match_for_user(current_user.id)

    # Find current user's rank
    db = get_db()
    user_rank = db.execute(
        'SELECT COUNT(*) + 1 FROM users WHERE score > ?',
        (current_user.score,)
    ).fetchone()[0]

    return render_template('leaderboard.html',
                          user=current_user,
                          top_users=top_users,
                          user_rank=user_rank,
                          active_match=active_match)

@main_bp.route('/practice')
@login_required
def practice():
    # Check if user is already in an active match
    active_match = Match.get_active_match_for_user(current_user.id)

    # Get all available topics from questions
    questions_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'static', 'questions.json')
    with open(questions_path, 'r') as f:
        questions = json.load(f)

    # Extract unique subjects and topics
    subjects = set()
    topics = {}

    for question in questions:
        if 'subject' in question:
            subject = question['subject']
            subjects.add(subject)

            if subject not in topics:
                topics[subject] = set()

            if 'topic' in question:
                topics[subject].add(question['topic'])

    # Convert sets to sorted lists for template rendering
    subjects = sorted(list(subjects))
    for subject in topics:
        topics[subject] = sorted(list(topics[subject]))

    return render_template('practice.html',
                          user=current_user,
                          subjects=subjects,
                          topics=topics,
                          active_match=active_match)

@main_bp.route('/practice/<subject>/<topic>')
@login_required
def practice_topic(subject, topic):
    # Check if user is already in an active match
    active_match = Match.get_active_match_for_user(current_user.id)

    # Load questions
    questions_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'static', 'questions.json')
    with open(questions_path, 'r') as f:
        all_questions = json.load(f)

    # Filter questions by subject and topic
    if subject == 'All':
        if topic == 'All':
            filtered_questions = all_questions
        else:
            filtered_questions = [q for q in all_questions if q.get('topic') == topic]
    else:
        if topic == 'All':
            filtered_questions = [q for q in all_questions if q.get('subject') == subject]
        else:
            filtered_questions = [q for q in all_questions if q.get('subject') == subject and q.get('topic') == topic]

    # Randomly select up to 5 questions
    if len(filtered_questions) > 5:
        practice_questions = random.sample(filtered_questions, 5)
    else:
        practice_questions = filtered_questions

    return render_template('practice_test.html',
                          user=current_user,
                          subject=subject,
                          topic=topic,
                          questions=practice_questions,
                          active_match=active_match)

@main_bp.route('/profile')
@login_required
def profile():
    # Get user's active match if any
    active_match = Match.get_active_match_for_user(current_user.id)

    # Get user's practice statistics
    practice_stats = PracticeResult.get_user_stats(current_user.id)

    # Get recent practice results
    recent_results = PracticeResult.get_by_user_id(current_user.id, limit=5)

    # Find current user's rank
    db = get_db()
    user_rank = db.execute(
        'SELECT COUNT(*) + 1 FROM users WHERE score > ?',
        (current_user.score,)
    ).fetchone()[0]

    # Calculate match statistics
    total_completed_matches = db.execute(
        '''SELECT COUNT(*) FROM matches
           WHERE (user1_id = ? OR user2_id = ?) AND status = 'completed' ''',
        (current_user.id, current_user.id)
    ).fetchone()[0]

    # Count matches won (where user is the winner)
    matches_won = db.execute(
        '''SELECT COUNT(*) FROM matches
           WHERE winner_id = ? AND status = 'completed' ''',
        (current_user.id,)
    ).fetchone()[0]

    # Count drawn matches (where winner_id is NULL)
    matches_drawn = db.execute(
        '''SELECT COUNT(*) FROM matches
           WHERE (user1_id = ? OR user2_id = ?) AND status = 'completed' AND winner_id IS NULL''',
        (current_user.id, current_user.id)
    ).fetchone()[0]

    # Calculate matches lost (total - won - drawn)
    matches_lost = total_completed_matches - matches_won - matches_drawn

    # Create match statistics dictionary
    match_stats = {
        'played': total_completed_matches,
        'won': matches_won,
        'drawn': matches_drawn,
        'lost': matches_lost
    }

    return render_template('profile.html',
                          user=current_user,
                          active_match=active_match,
                          practice_stats=practice_stats,
                          recent_results=recent_results,
                          user_rank=user_rank,
                          match_stats=match_stats)

@main_bp.route('/save-practice-results', methods=['POST'])
@login_required
def save_practice_results():
    # Get form data
    subject = request.form.get('subject', 'Unknown')
    topic = request.form.get('topic', 'Unknown')
    correct_answers = int(request.form.get('correct_answers', 0))
    total_questions = int(request.form.get('total_questions', 0))

    # Save the practice result
    result = PracticeResult.create(
        user_id=current_user.id,
        subject=subject,
        topic=topic,
        correct_answers=correct_answers,
        total_questions=total_questions
    )

    # Flash a success message
    flash(f'Practice results saved! You scored {correct_answers}/{total_questions} ({result.score_percentage}%)', 'success')

    # Redirect to the profile page
    return redirect(url_for('main.profile'))

@main_bp.route('/solve-question', methods=['GET', 'POST'])
@login_required
def solve_question():
    if request.method == 'POST':
        # Handle image upload or text input
        question_text = request.form.get('question_text', '')
        subject = request.form.get('subject', '')

        # Check if an image was uploaded
        if 'question_image' in request.files and request.files['question_image'].filename:
            # In a real implementation, we would process the image here
            # For now, we'll just acknowledge the image upload
            question_text += "\n[Note: An image was uploaded with this question. Image processing is not yet implemented.]"

        # Import the OpenAI helper
        from app.utils.openai_helper import solve_jee_question

        try:
            # Call the OpenAI API to solve the question
            solution = solve_jee_question(question_text, subject)

            # Log the solution for debugging
            print(f"Question: {question_text}")
            print(f"Solution: {solution}")

        except Exception as e:
            # If there's an error, provide a fallback solution
            print(f"Error solving question: {str(e)}")
            solution = {
                'answer': 'Sorry, I encountered an error while solving this question.',
                'explanation': f'Error details: {str(e)}\n\nPlease try again with a different question or contact support if the issue persists.'
            }

        return render_template('solution.html',
                             user=current_user,
                             question=question_text,
                             solution=solution)

    # GET request - show the form
    active_match = Match.get_active_match_for_user(current_user.id)
    return render_template('solve_question.html',
                          user=current_user,
                          active_match=active_match)
