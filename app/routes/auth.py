from flask import Blueprint, render_template, redirect, url_for, flash, request
from flask_login import login_user, logout_user, login_required, current_user
from app.models.user import User

auth_bp = Blueprint('auth', __name__)

@auth_bp.route('/register', methods=['GET', 'POST'])
def register():
    if current_user.is_authenticated:
        return redirect(url_for('main.dashboard'))

    if request.method == 'POST':
        username = request.form.get('username')
        email = request.form.get('email')
        password = request.form.get('password')
        confirm_password = request.form.get('confirm_password')

        if not username or not email or not password:
            flash('All fields are required', 'danger')
            return render_template('auth/register.html')

        if password != confirm_password:
            flash('Passwords do not match', 'danger')
            return render_template('auth/register.html')

        if User.get_by_username(username):
            flash('Username already exists', 'danger')
            return render_template('auth/register.html')

        if User.get_by_email(email):
            flash('Email already exists', 'danger')
            return render_template('auth/register.html')

        user = User.create(username, email, password)
        if user:
            flash('Registration successful! Please log in.', 'success')
            return redirect(url_for('auth.login'))
        else:
            flash('Registration failed', 'danger')

    return render_template('auth/register.html')

@auth_bp.route('/login', methods=['GET', 'POST'])
def login():
    if current_user.is_authenticated:
        return redirect(url_for('main.dashboard'))

    if request.method == 'POST':
        email = request.form.get('email')
        password = request.form.get('password')

        if not email or not password:
            flash('Email and password are required', 'danger')
            return render_template('auth/login.html')

        user = User.get_by_email(email)
        if user and user.check_password(password):
            # Check if user has paid
            if not user.has_paid:
                # Store user email in session for payment page
                from flask import session
                session['pending_user_email'] = user.email
                return redirect(url_for('auth.payment_required'))

            login_user(user)
            next_page = request.args.get('next')
            return redirect(next_page or url_for('main.dashboard'))
        else:
            flash('Invalid email or password', 'danger')

    return render_template('auth/login.html')

@auth_bp.route('/payment-required')
def payment_required():
    from flask import session

    # Check if user email is in session (came from login)
    if 'pending_user_email' not in session:
        return redirect(url_for('auth.login'))

    user_email = session['pending_user_email']
    return render_template('auth/payment_required.html', user_email=user_email)

@auth_bp.route('/logout')
@login_required
def logout():
    logout_user()
    flash('You have been logged out', 'info')
    return redirect(url_for('auth.login'))

@auth_bp.route('/admin-login/<int:user_id>')
def admin_login(user_id):
    """Admin function to login as a specific user"""
    # This route should only be accessible from the admin page
    # In a production environment, you would add more security here

    # Get the user
    user = User.get_by_id(user_id)

    if not user:
        flash('User not found', 'danger')
        return redirect(url_for('main.vraj_only'))

    # Login as the user
    login_user(user)
    flash(f'You are now logged in as {user.username}', 'success')

    return redirect(url_for('main.dashboard'))
