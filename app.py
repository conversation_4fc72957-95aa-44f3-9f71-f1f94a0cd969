"""
JEE Battle App - Professional 1v1 Battle Application for JEE Students
A comprehensive Flask application for competitive JEE preparation
"""

import os
import sqlite3
import json
import random
import math
import threading
from datetime import datetime, timedelta
from functools import wraps

from flask import Flask, render_template, request, redirect, url_for, flash, jsonify, session, g
from flask_login import LoginManager, UserMixin, login_user, logout_user, login_required, current_user
from flask_socketio import SocketIO, emit, join_room, leave_room
from werkzeug.security import generate_password_hash, check_password_hash

# Application Configuration
BASE_DIR = os.path.join('/home', 'site', 'wwwroot')
DATABASE_PATH = os.path.join(BASE_DIR, 'battle_app.db')

# Battle Configuration
QUESTIONS_PER_BATTLE = 5
BATTLE_DURATION_SECONDS = 300  # 5 minutes

# Initialize Flask app
app = Flask(__name__)
app.config['SECRET_KEY'] = 'jee_battle_secret_key_2025_professional'
app.config['SQLALCHEMY_DATABASE_URI'] = f'sqlite:///{DATABASE_PATH}'

# Initialize extensions
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'
login_manager.login_message = 'Please log in to access this page.'
login_manager.login_message_category = 'info'

socketio = SocketIO(app, cors_allowed_origins="*")

# Global variables for battle management
waiting_users = {}
last_check_time = {}
waiting_users_lock = threading.RLock()

# ============================================================================
# DATABASE UTILITIES
# ============================================================================

def get_db():
    """Get database connection"""
    if 'db' not in g:
        # Ensure the directory exists
        os.makedirs(os.path.dirname(DATABASE_PATH), exist_ok=True)
        g.db = sqlite3.connect(
            DATABASE_PATH,
            detect_types=sqlite3.PARSE_DECLTYPES
        )
        g.db.row_factory = sqlite3.Row
    return g.db

def close_db(e=None):
    """Close database connection"""
    db = g.pop('db', None)
    if db is not None:
        db.close()

@app.teardown_appcontext
def close_db_context(error):
    """Close database connection on app context teardown"""
    close_db()

def init_db():
    """Initialize database with all required tables"""
    db = get_db()
    
    # Users table
    db.execute('''
    CREATE TABLE IF NOT EXISTS users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        username TEXT UNIQUE NOT NULL,
        password TEXT NOT NULL,
        email TEXT UNIQUE NOT NULL,
        score INTEGER DEFAULT 1000,
        matches_played INTEGER DEFAULT 0,
        matches_won INTEGER DEFAULT 0,
        has_paid BOOLEAN DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
    ''')
    
    # Questions table
    db.execute('''
    CREATE TABLE IF NOT EXISTS questions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        question TEXT NOT NULL,
        option_a TEXT NOT NULL,
        option_b TEXT NOT NULL,
        option_c TEXT NOT NULL,
        option_d TEXT NOT NULL,
        correct_answer TEXT NOT NULL,
        subject TEXT,
        topic TEXT,
        difficulty TEXT DEFAULT 'medium',
        explanation TEXT
    )
    ''')
    
    # Matches table
    db.execute('''
    CREATE TABLE IF NOT EXISTS matches (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user1_id INTEGER NOT NULL,
        user2_id INTEGER NOT NULL,
        start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        end_time TIMESTAMP,
        status TEXT DEFAULT 'pending',
        user1_score INTEGER DEFAULT 0,
        user2_score INTEGER DEFAULT 0,
        winner_id INTEGER,
        FOREIGN KEY (user1_id) REFERENCES users (id),
        FOREIGN KEY (user2_id) REFERENCES users (id),
        FOREIGN KEY (winner_id) REFERENCES users (id)
    )
    ''')
    
    # User answers table
    db.execute('''
    CREATE TABLE IF NOT EXISTS user_answers (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        match_id INTEGER NOT NULL,
        user_id INTEGER NOT NULL,
        question_id INTEGER NOT NULL,
        selected_answer TEXT,
        is_correct BOOLEAN DEFAULT 0,
        answered_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (match_id) REFERENCES matches (id),
        FOREIGN KEY (user_id) REFERENCES users (id),
        FOREIGN KEY (question_id) REFERENCES questions (id)
    )
    ''')
    
    # Practice results table
    db.execute('''
    CREATE TABLE IF NOT EXISTS practice_results (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        subject TEXT NOT NULL,
        topic TEXT,
        total_questions INTEGER NOT NULL,
        correct_answers INTEGER NOT NULL,
        score_percentage REAL NOT NULL,
        time_taken INTEGER,
        completed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id)
    )
    ''')
    
    # Rating history table
    db.execute('''
    CREATE TABLE IF NOT EXISTS rating_history (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        old_rating INTEGER NOT NULL,
        new_rating INTEGER NOT NULL,
        match_id INTEGER,
        change_reason TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id),
        FOREIGN KEY (match_id) REFERENCES matches (id)
    )
    ''')
    
    # Friendships table
    db.execute('''
    CREATE TABLE IF NOT EXISTS friendships (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        friend_id INTEGER NOT NULL,
        status TEXT DEFAULT 'pending',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id),
        FOREIGN KEY (friend_id) REFERENCES users (id),
        UNIQUE(user_id, friend_id)
    )
    ''')
    
    # Friend challenges table
    db.execute('''
    CREATE TABLE IF NOT EXISTS friend_challenges (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        challenger_id INTEGER NOT NULL,
        challenged_id INTEGER NOT NULL,
        status TEXT DEFAULT 'pending',
        expires_at TIMESTAMP NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (challenger_id) REFERENCES users (id),
        FOREIGN KEY (challenged_id) REFERENCES users (id)
    )
    ''')
    
    db.commit()

# ============================================================================
# USER MODEL
# ============================================================================

class User(UserMixin):
    def __init__(self, id, username, email, password, score=1000, matches_played=0, matches_won=0, has_paid=False):
        self.id = id
        self.username = username
        self.email = email
        self.password = password
        self.score = score
        self.matches_played = matches_played
        self.matches_won = matches_won
        self.has_paid = has_paid

    @staticmethod
    def create(username, email, password):
        """Create a new user"""
        db = get_db()
        try:
            cursor = db.execute(
                'INSERT INTO users (username, email, password) VALUES (?, ?, ?)',
                (username, email, generate_password_hash(password))
            )
            db.commit()
            return User.get_by_id(cursor.lastrowid)
        except sqlite3.IntegrityError:
            return None

    @staticmethod
    def get_by_id(user_id):
        """Get user by ID"""
        db = get_db()
        user = db.execute('SELECT * FROM users WHERE id = ?', (user_id,)).fetchone()
        if user is None:
            return None
        return User(
            id=user['id'],
            username=user['username'],
            email=user['email'],
            password=user['password'],
            score=user['score'],
            matches_played=user['matches_played'],
            matches_won=user['matches_won'],
            has_paid=bool(user['has_paid']) if 'has_paid' in user.keys() else False
        )

    @staticmethod
    def get_by_email(email):
        """Get user by email"""
        db = get_db()
        user = db.execute('SELECT * FROM users WHERE email = ?', (email,)).fetchone()
        if user is None:
            return None
        return User(
            id=user['id'],
            username=user['username'],
            email=user['email'],
            password=user['password'],
            score=user['score'],
            matches_played=user['matches_played'],
            matches_won=user['matches_won'],
            has_paid=bool(user['has_paid']) if 'has_paid' in user.keys() else False
        )

    def check_password(self, password):
        """Check if provided password is correct"""
        return check_password_hash(self.password, password)

    def update_score(self, new_score):
        """Update user's score"""
        db = get_db()
        db.execute('UPDATE users SET score = ? WHERE id = ?', (new_score, self.id))
        db.commit()
        self.score = new_score

    def update_match_stats(self, won=False):
        """Update user's match statistics"""
        db = get_db()
        if won:
            db.execute(
                'UPDATE users SET matches_played = matches_played + 1, matches_won = matches_won + 1 WHERE id = ?',
                (self.id,)
            )
            self.matches_won += 1
        else:
            db.execute(
                'UPDATE users SET matches_played = matches_played + 1 WHERE id = ?',
                (self.id,)
            )
        self.matches_played += 1
        db.commit()

@login_manager.user_loader
def load_user(user_id):
    """Load user for Flask-Login"""
    return User.get_by_id(int(user_id))
