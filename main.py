import os
from app import create_app, socketio

# Set environment variables directly instead of using .env file
os.environ['FLASK_APP'] = 'app.py'
os.environ['FLASK_ENV'] = 'production'

# Battle configuration settings
# You can modify these values to change the battle parameters
BATTLE_CONFIG = {
    'QUESTIONS_PER_BATTLE': 5,  # Number of questions per battle (default: 3)
    'BATTLE_DURATION_SECONDS': 300,  # Battle duration in seconds (default: 15)
}

# Pass the configuration to the app
app = create_app(config=BATTLE_CONFIG)

if __name__ == "__main__":
    socketio.run(app, host="0.0.0.0", port=8000)
